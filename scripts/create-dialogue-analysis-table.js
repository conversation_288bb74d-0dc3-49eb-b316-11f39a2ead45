import {sequelize} from '../db.js';
import {DataTypes} from 'sequelize';

async function createDialogueAnalysisTable() {
  try {
    console.log('Starting to create dialogue_analyses table...');
    
    const queryInterface = sequelize.getQueryInterface();
    
    // Check if table already exists
    const tableExists = await queryInterface.showAllTables()
      .then(tables => tables.includes('dialogue_analyses'));
    
    if (tableExists) {
      console.log('Table dialogue_analyses already exists. Dropping existing table...');
      await queryInterface.dropTable('dialogue_analyses');
    }

    await queryInterface.createTable('dialogue_analyses', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      dialogueId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'dialogues',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'bots',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      externalUserId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'external_users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      analysis: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      prompt: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      model: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'gpt-4o'
      },
      temperature: {
        type: DataTypes.FLOAT,
        allowNull: false,
        defaultValue: 0.2
      },
      tokenUsage: {
        type: DataTypes.JSON,
        allowNull: true
      },
      lastMessageId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'dialogue_messages',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      messageCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      crmExternalId: {
        type: DataTypes.STRING,
        allowNull: true
      },
      crmIntegrationType: {
        type: DataTypes.STRING,
        allowNull: true
      },
      crmSentAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      crmError: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      status: {
        type: DataTypes.ENUM('pending', 'completed', 'failed', 'sent_to_crm'),
        defaultValue: 'pending',
        allowNull: false
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    });

    console.log('Table dialogue_analyses created successfully.');

    // Add indexes
    console.log('Adding indexes...');
    await queryInterface.addIndex('dialogue_analyses', ['dialogueId'], {
      name: 'dialogue_id_index'
    });

    await queryInterface.addIndex('dialogue_analyses', ['botId'], {
      name: 'bot_id_index'
    });

    await queryInterface.addIndex('dialogue_analyses', ['externalUserId'], {
      name: 'external_user_id_index'
    });

    await queryInterface.addIndex('dialogue_analyses', ['status'], {
      name: 'status_index'
    });

    await queryInterface.addIndex('dialogue_analyses', ['createdAt'], {
      name: 'created_at_index'
    });

    await queryInterface.addIndex('dialogue_analyses', ['dialogueId', 'lastMessageId'], {
      unique: true,
      name: 'unique_dialogue_message_analysis'
    });

    console.log('All indexes added successfully.');
    console.log('Table creation and indexing completed successfully!');
  } catch (error) {
    console.error('Error creating dialogue_analyses table:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// Execute the function
createDialogueAnalysisTable();
