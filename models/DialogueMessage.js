import {DataTypes} from 'sequelize';
import {sequelize} from '../db.js';

const DialogueMessage = sequelize.define('DialogueMessage', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    dialogId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
            model: 'dialogues',
            key: 'id'
        }
    },
    direction: {
        type: DataTypes.ENUM('user', 'assistant', 'system'),
        allowNull: false
    },
    message: {
        type: DataTypes.TEXT('long'),
        allowNull: true
    },
    aiTokenCount: {
        type: DataTypes.JSON,
        allowNull: true
    },
    telegramMessageId: {
        type: DataTypes.STRING,
        allowNull: true
    },
    threadMessageId: {
        type: DataTypes.STRING,
        allowNull: true
    },
    runId: {
        type: DataTypes.STRING,
        allowNull: true
    },
    err: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: 0
    },
    errMessage: {
        type: DataTypes.STRING,
        allowNull: true
    },
    answerTo: {
        type: DataTypes.UUID,
        allowNull: true
    },
    createdAt: {
        type: DataTypes.DATE(3)
    },
    updatedAt: {
        type: DataTypes.DATE(3)
    }
}, {
    tableName: 'dialogue_messages',
    timestamps: true,
    indexes: [
        {
            fields: ['dialogId'],
            name: 'dialog_id_index'
        },
        {
            fields: ['createdAt'],
            name: 'created_at_index'
        }
    ]
});

export default DialogueMessage;